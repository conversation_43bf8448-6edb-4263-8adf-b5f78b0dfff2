('D:\\Pocket\\build\\pocket\\PYZ-00.pyz',
 [('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('charset_normalizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('flask',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'C:\\Python313\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\Pocket\\.venv\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\Pocket\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE')])
