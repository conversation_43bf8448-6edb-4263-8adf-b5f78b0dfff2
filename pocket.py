from flask import Flask, render_template, request, redirect, url_for, jsonify
import pandas as pd
import os
import sys
import shutil
from datetime import datetime

app = Flask(__name__)

# --------- PyInstaller Compatibility ---------
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

BUNDLED_DATA_FILE = resource_path('data.xlsx')
APP_FOLDER = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
DATA_FILE = os.path.join(APP_FOLDER, 'data.xlsx')

if not os.path.exists(DATA_FILE):
    shutil.copy(BUNDLED_DATA_FILE, DATA_FILE)

# --------- Helpers ---------
def load_data():
    if os.path.exists(DATA_FILE):
        df = pd.read_excel(DATA_FILE)
        df.fillna('', inplace=True)
        data = df.to_dict(orient='records')

        # Ensure all items have access_count field for backward compatibility
        for item in data:
            if 'access_count' not in item or pd.isna(item['access_count']):
                item['access_count'] = 0

        return data
    return []

def save_data(data):
    df = pd.DataFrame(data)
    df.to_excel(DATA_FILE, index=False)



# --------- Routes ---------
@app.route('/')
def index():
    data = load_data()
    print(f"DEBUG: Loaded {len(data)} items from data file")  # Debug log
    search = request.args.get('search', '').lower()
    tag_filter = request.args.get('tag', '').lower()
    print(f"DEBUG: Search='{search}', Tag='{tag_filter}'")  # Debug log

    # 🔍 Search and tag filter BEFORE pagination
    if search:
        import re

        def parse_search_query(search_input):
            """Parse search query with AND, OR, NOT operators"""
            search_input = search_input.strip()

            # Handle quoted phrases first
            quoted_pattern = r'"([^"]*)"'
            quoted_matches = re.findall(quoted_pattern, search_input)
            quoted_placeholders = {}

            # Replace quoted phrases with placeholders
            for i, quoted_term in enumerate(quoted_matches):
                placeholder = f"__QUOTED_{i}__"
                quoted_placeholders[placeholder] = quoted_term.strip()
                search_input = search_input.replace(f'"{quoted_term}"', placeholder, 1)

            # Parse NOT conditions first (highest precedence)
            not_terms = []
            not_pattern = r'\bnot\s+(\S+)'
            not_matches = re.findall(not_pattern, search_input, re.IGNORECASE)
            for not_term in not_matches:
                # Replace placeholder back to original quoted term if needed
                if not_term in quoted_placeholders:
                    not_terms.append(quoted_placeholders[not_term])
                else:
                    not_terms.append(not_term)

            # Remove NOT conditions from search string
            search_input = re.sub(r'\bnot\s+\S+', '', search_input, flags=re.IGNORECASE).strip()

            # Parse AND conditions (medium precedence)
            and_groups = []
            and_pattern = r'\band\s+'
            and_parts = re.split(and_pattern, search_input, flags=re.IGNORECASE)

            if len(and_parts) > 1:
                # We have AND conditions
                for part in and_parts:
                    part = part.strip()
                    if part:
                        # Handle OR within AND groups
                        or_terms = []
                        or_parts = re.split(r'\bor\s+', part, flags=re.IGNORECASE)
                        for or_part in or_parts:
                            or_part = or_part.strip()
                            if or_part:
                                # Replace placeholder back to original quoted term if needed
                                if or_part in quoted_placeholders:
                                    or_terms.append(quoted_placeholders[or_part])
                                else:
                                    # Split by spaces and commas for individual terms
                                    terms = [term.strip() for term in or_part.replace(',', ' ').split() if term.strip()]
                                    or_terms.extend(terms)
                        if or_terms:
                            and_groups.append(or_terms)
            else:
                # No AND conditions, parse OR conditions (lowest precedence)
                or_terms = []
                or_parts = re.split(r'\bor\s+', search_input, flags=re.IGNORECASE)
                for or_part in or_parts:
                    or_part = or_part.strip()
                    if or_part:
                        # Replace placeholder back to original quoted term if needed
                        if or_part in quoted_placeholders:
                            or_terms.append(quoted_placeholders[or_part])
                        else:
                            # Split by spaces and commas for individual terms
                            terms = [term.strip() for term in or_part.replace(',', ' ').split() if term.strip()]
                            or_terms.extend(terms)

                if or_terms:
                    and_groups = [or_terms]  # Single OR group

            return and_groups, not_terms

        def term_matches(term, item_text):
            """Check if a term matches in the item text"""
            term_lower = term.lower()
            # For single words, use word boundaries to avoid partial matches
            if ' ' not in term_lower:
                return bool(re.search(r'\b' + re.escape(term_lower) + r'\b', item_text))
            else:
                # For phrases, use exact substring matching
                return term_lower in item_text

        def matches_search(item):
            """Check if item matches the search criteria"""
            item_text = (str(item['title']) + ' ' + str(item['url']) + ' ' + str(item.get('tags', ''))).lower()

            and_groups, not_terms = parse_search_query(search)

            # Check NOT conditions first (if any NOT term matches, exclude the item)
            for not_term in not_terms:
                if term_matches(not_term, item_text):
                    return False

            # If no AND groups, return True (no positive conditions to check)
            if not and_groups:
                return True

            # Check AND conditions (all groups must have at least one match)
            for or_group in and_groups:
                group_matched = False
                for term in or_group:
                    if term_matches(term, item_text):
                        group_matched = True
                        break

                # If any AND group doesn't match, the item doesn't match
                if not group_matched:
                    return False

            return True

        data = [item for item in data if matches_search(item)]
    if tag_filter:
        data = [item for item in data if tag_filter in str(item.get('tags', '')).lower()]

    # 📊 Sort by access_count (most accessed first)
    data.sort(key=lambda x: x.get('access_count', 0), reverse=True)

    print(f"DEBUG: Rendering template with {len(data)} items")  # Debug log
    return render_template('index.html', items=data, search=search,
                           selected_tag=tag_filter)

@app.route('/upload', methods=['POST'])
def upload():
    file = request.files['file']
    if file:
        file.save(DATA_FILE)
    return redirect(url_for('index'))

@app.route('/add', methods=['POST'])
def add():
    data = load_data()
    new_item = {
        'title': request.form['title'],
        'url': request.form['url'],
        'tags': request.form.get('tags', ''),
        'time_added': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': 'unread',
        'access_count': 0
    }
    data.append(new_item)
    save_data(data)
    return redirect(url_for('index'))

@app.route('/delete', methods=['POST'])
def delete():
    title = request.form['title']
    url = request.form['url']
    data = load_data()
    data = [item for item in data if not (item['title'] == title and item['url'] == url)]
    save_data(data)
    return redirect(url_for('index'))

@app.route('/api/items')
def api_items():
    data = load_data()
    return jsonify(data)



@app.route('/api/access-url', methods=['POST'])
def api_access_url():
    """API endpoint to track URL access"""
    try:
        url = request.json.get('url')
        if not url:
            return jsonify({'success': False, 'error': 'URL is required'}), 400

        data = load_data()
        updated = False

        for item in data:
            if item['url'] == url:
                item['access_count'] = item.get('access_count', 0) + 1
                updated = True
                break

        if updated:
            save_data(data)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'URL not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/update-item', methods=['POST'])
def api_update_item():
    """API endpoint to update item title or tags"""
    try:
        url = request.json.get('url')
        field = request.json.get('field')  # 'title' or 'tags'
        value = request.json.get('value')

        if not url or not field:
            return jsonify({'success': False, 'error': 'URL and field are required'}), 400

        if field not in ['title', 'tags', 'url']:
            return jsonify({'success': False, 'error': 'Field must be title, tags, or url'}), 400

        data = load_data()
        updated = False

        for item in data:
            if item['url'] == url:
                if field == 'url':
                    # Special handling for URL updates
                    if not value or not value.strip():
                        return jsonify({'success': False, 'error': 'URL cannot be empty'}), 400
                    print(f"DEBUG: Updating URL from '{url}' to '{value.strip()}'")  # Debug log
                    item['url'] = value.strip()
                else:
                    print(f"DEBUG: Updating {field} for URL '{url}' to '{value}'")  # Debug log
                    item[field] = value or ''
                updated = True
                break

        if updated:
            save_data(data)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'URL not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/check-duplicates')
def api_check_duplicates():
    """API endpoint to check for duplicate URLs"""
    try:
        data = load_data()
        url_groups = {}

        # Group items by URL
        for item in data:
            url = item.get('url', '').strip().lower()
            if url:
                if url not in url_groups:
                    url_groups[url] = []
                url_groups[url].append(item)

        # Find duplicates (groups with more than one item)
        duplicates = []
        for url, items in url_groups.items():
            if len(items) > 1:
                duplicates.append({
                    'url': url,
                    'count': len(items),
                    'items': items
                })

        # Sort by count (most duplicates first)
        duplicates.sort(key=lambda x: x['count'], reverse=True)

        return jsonify({
            'success': True,
            'duplicates': duplicates,
            'total_duplicates': len(duplicates),
            'total_duplicate_items': sum(d['count'] for d in duplicates)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Quick search buttons data file
QUICK_SEARCH_FILE = os.path.join(APP_FOLDER, 'quick_searches.json')

def load_quick_searches():
    """Load quick search button configurations"""
    try:
        if os.path.exists(QUICK_SEARCH_FILE):
            import json
            with open(QUICK_SEARCH_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception:
        pass

    # Return default empty buttons
    return [{'label': '', 'search': ''} for _ in range(8)]

def save_quick_searches(quick_searches):
    """Save quick search button configurations"""
    try:
        import json
        with open(QUICK_SEARCH_FILE, 'w', encoding='utf-8') as f:
            json.dump(quick_searches, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

@app.route('/api/quick-searches')
def api_get_quick_searches():
    """API endpoint to get quick search buttons"""
    try:
        quick_searches = load_quick_searches()
        return jsonify({'success': True, 'quick_searches': quick_searches})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/quick-searches', methods=['POST'])
def api_save_quick_searches():
    """API endpoint to save quick search buttons"""
    try:
        quick_searches = request.json.get('quick_searches', [])

        # Validate data
        if not isinstance(quick_searches, list) or len(quick_searches) != 8:
            return jsonify({'success': False, 'error': 'Invalid quick searches data'}), 400

        # Ensure each item has required fields
        for i, item in enumerate(quick_searches):
            if not isinstance(item, dict):
                quick_searches[i] = {'label': '', 'search': ''}
            else:
                quick_searches[i] = {
                    'label': str(item.get('label', '')),
                    'search': str(item.get('search', ''))
                }

        if save_quick_searches(quick_searches):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Failed to save quick searches'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
