from flask import Flask, render_template, request, redirect, url_for, jsonify
import pandas as pd
import os
import sys
import shutil
import math
from datetime import datetime
import requests
import concurrent.futures

app = Flask(__name__)

# --------- PyInstaller Compatibility ---------
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

BUNDLED_DATA_FILE = resource_path('data.xlsx')
APP_FOLDER = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
DATA_FILE = os.path.join(APP_FOLDER, 'data.xlsx')

if not os.path.exists(DATA_FILE):
    shutil.copy(BUNDLED_DATA_FILE, DATA_FILE)

# --------- Helpers ---------
def load_data():
    if os.path.exists(DATA_FILE):
        df = pd.read_excel(DATA_FILE)
        df.fillna('', inplace=True)
        data = df.to_dict(orient='records')

        # Ensure all items have access_count field for backward compatibility
        for item in data:
            if 'access_count' not in item or pd.isna(item['access_count']):
                item['access_count'] = 0

        return data
    return []

def save_data(data):
    df = pd.DataFrame(data)
    df.to_excel(DATA_FILE, index=False)

def check_url_status(url):
    """Check if a URL is accessible and return status info"""
    try:
        # Add http:// if no protocol is specified
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        response = requests.head(url, timeout=10, allow_redirects=True)
        return {
            'url': url,
            'status_code': response.status_code,
            'status': 'accessible' if response.status_code < 400 else 'error',
            'error': None
        }
    except requests.exceptions.RequestException as e:
        return {
            'url': url,
            'status_code': None,
            'status': 'error',
            'error': str(e)
        }

def check_all_urls():
    """Check all URLs in the database and return their status"""
    data = load_data()
    urls = [item['url'] for item in data if item.get('url')]

    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_url = {executor.submit(check_url_status, url): url for url in urls}
        for future in concurrent.futures.as_completed(future_to_url):
            result = future.result()
            results.append(result)

    return results

# --------- Routes ---------
@app.route('/')
def index():
    data = load_data()
    search = request.args.get('search', '').lower()
    tag_filter = request.args.get('tag', '').lower()

    # 🔍 Search and tag filter BEFORE pagination
    if search:
        import re

        search_terms = []
        search_input = search.strip()

        # Handle quoted phrases first
        quoted_pattern = r'"([^"]*)"'
        quoted_matches = re.findall(quoted_pattern, search_input)

        # Add quoted phrases as exact terms
        for quoted_term in quoted_matches:
            if quoted_term.strip():
                search_terms.append(quoted_term.strip())

        # Remove quoted phrases from the search string
        remaining_search = re.sub(quoted_pattern, '', search_input).strip()

        if remaining_search:
            # Split remaining terms by 'or' (case insensitive)
            or_parts = [part.strip() for part in remaining_search.replace(' or ', '|').split('|')]
            for part in or_parts:
                # Split each part by spaces and commas
                terms = [term.strip() for term in part.replace(',', ' ').split() if term.strip()]
                search_terms.extend(terms)

        # Remove duplicates while preserving order
        search_terms = list(dict.fromkeys(search_terms))

        # Filter items that contain ANY of the search terms (using word boundaries for better matching)
        def matches_search(item):
            item_text = (str(item['title']) + ' ' + str(item['url']) + ' ' + str(item.get('tags', ''))).lower()

            for term in search_terms:
                term_lower = term.lower()
                # For single words, use word boundaries to avoid partial matches
                if ' ' not in term_lower:
                    # Use word boundary regex for single words
                    if re.search(r'\b' + re.escape(term_lower) + r'\b', item_text):
                        return True
                else:
                    # For phrases, use exact substring matching
                    if term_lower in item_text:
                        return True
            return False

        data = [item for item in data if matches_search(item)]
    if tag_filter:
        data = [item for item in data if tag_filter in str(item.get('tags', '')).lower()]

    # 📊 Sort by access_count (most accessed first)
    data.sort(key=lambda x: x.get('access_count', 0), reverse=True)

    page = int(request.args.get('page', 1))
    per_page = 10
    total_items = len(data)
    total_pages = max(1, math.ceil(total_items / per_page))
    data_paginated = data[(page - 1) * per_page: page * per_page]

    return render_template('index.html', items=data_paginated, search=search,
                           selected_tag=tag_filter, page=page, total_pages=total_pages)

@app.route('/upload', methods=['POST'])
def upload():
    file = request.files['file']
    if file:
        file.save(DATA_FILE)
    return redirect(url_for('index'))

@app.route('/add', methods=['POST'])
def add():
    data = load_data()
    new_item = {
        'title': request.form['title'],
        'url': request.form['url'],
        'tags': request.form.get('tags', ''),
        'time_added': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': 'unread',
        'access_count': 0
    }
    data.append(new_item)
    save_data(data)
    return redirect(url_for('index'))

@app.route('/delete', methods=['POST'])
def delete():
    title = request.form['title']
    url = request.form['url']
    data = load_data()
    data = [item for item in data if not (item['title'] == title and item['url'] == url)]
    save_data(data)
    return redirect(url_for('index'))

@app.route('/api/items')
def api_items():
    data = load_data()
    return jsonify(data)

@app.route('/api/check-urls')
def api_check_urls():
    """API endpoint to check all URLs"""
    try:
        results = check_all_urls()
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/access-url', methods=['POST'])
def api_access_url():
    """API endpoint to track URL access"""
    try:
        url = request.json.get('url')
        if not url:
            return jsonify({'success': False, 'error': 'URL is required'}), 400

        data = load_data()
        updated = False

        for item in data:
            if item['url'] == url:
                item['access_count'] = item.get('access_count', 0) + 1
                updated = True
                break

        if updated:
            save_data(data)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'URL not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
