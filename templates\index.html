<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pocket Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .search-section {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 50px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 8px 16px;
            font-size: 14px;
        }

        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .search-help {
            font-size: 14px;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }

        .add-form {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .summary {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-height: 70vh;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            position: sticky;
            top: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 10;
        }

        thead th {
            padding: 20px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            text-align: left;
            border: none;
        }

        tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            cursor: pointer;
        }

        tbody tr.selected {
            background: rgba(102, 126, 234, 0.1);
        }

        td {
            padding: 20px;
            border: none;
            vertical-align: middle;
        }

        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .editable-cell:hover {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .editable-cell.editing {
            padding: 0;
        }

        .editable-input {
            width: 100%;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 15px;
            font-size: 16px;
            font-family: inherit;
            background: #fff;
        }

        .editable-input:focus {
            outline: none;
            border-color: #764ba2;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .access-count {
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            padding: 8px 16px;
            display: inline-block;
            min-width: 40px;
        }

        .edit-hint {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
            cursor: pointer;
        }

        .dropdown-toggle:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 200px;
            overflow: hidden;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Modal Styles for Duplicate Results */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        .duplicate-group {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .duplicate-url {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .duplicate-items {
            margin-left: 20px;
        }

        .duplicate-item {
            background: white;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .duplicate-item-title {
            font-weight: 600;
            color: #333;
        }

        .duplicate-item-tags {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        /* Quick Search Styles */
        .quick-search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .quick-search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .quick-search-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .quick-search-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .quick-search-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 20px 15px;
            min-height: 80px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .quick-search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .quick-search-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .quick-search-btn.active:hover {
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        .quick-search-btn.empty {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px dashed #dee2e6;
            color: #6c757d;
        }

        .quick-search-btn.empty:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .quick-search-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .quick-search-label {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
            word-break: break-word;
        }

        .quick-search-btn.empty .quick-search-label {
            font-style: italic;
        }

        /* Edit Modal Styles */
        .edit-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .edit-modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .edit-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .edit-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e1e5e9;
        }

        .edit-item h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }

        .edit-input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .edit-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .edit-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        /* Responsive Design for Quick Search */
        @media (max-width: 768px) {
            .quick-search-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .quick-search-btn {
                min-height: 70px;
                padding: 15px 10px;
            }

            .quick-search-icon {
                font-size: 20px;
                margin-bottom: 5px;
            }

            .quick-search-label {
                font-size: 12px;
            }

            .edit-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* Context Menu Styles */
        .context-menu {
            position: fixed;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 3000;
            min-width: 180px;
            overflow: hidden;
            display: none;
        }

        .context-menu-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .context-menu-item:last-child {
            border-bottom: none;
        }

        .context-menu-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .context-menu-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .context-menu-text {
            flex: 1;
            font-size: 14px;
        }

        /* URL Display Modal */
        .url-modal {
            display: none;
            position: fixed;
            z-index: 2500;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .url-modal-content {
            background: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .url-display {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #333;
        }

        .url-edit-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }

        .url-edit-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .search-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
            }

            .action-buttons {
                justify-content: center;
            }

            .form-row {
                flex-direction: column;
            }

            .table-container {
                border-radius: 15px;
            }

            thead th,
            td {
                padding: 15px 10px;
                font-size: 14px;
            }
        }

        /* Scrollbar Styling */
        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Pocket Viewer</h1>
            <p>Your personal bookmark collection, beautifully organized</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="search-section">
                <form method="GET" action="{{ url_for('index') }}" style="display: contents;">
                    <input type="text" name="search" class="search-input"
                           placeholder="Search with AND, OR, NOT operators (e.g., 'python and tutorial', 'linux not kali')..."
                           value="{{ search }}">
                    <button type="submit" class="btn btn-primary">Search</button>
                </form>

                <div class="action-buttons">
                    <a href="{{ url_for('index') }}" class="btn btn-secondary">Clear Filters</a>
                    <button type="button" class="btn btn-primary" onclick="document.querySelector('.add-form').style.display='block'">Add URL</button>

                    <!-- Tools Dropdown -->
                    <div class="dropdown">
                        <button type="button" class="btn btn-secondary dropdown-toggle" onclick="toggleDropdown()">
                            Tools ▼
                        </button>
                        <div class="dropdown-menu" id="toolsDropdown">
                            <a href="#" class="dropdown-item" onclick="checkDuplicates()">Check for Duplicate URLs</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-help">
                <strong>Search Operators:</strong><br>
                <span style="color: #667eea;">OR:</span> python or javascript (either term) |
                <span style="color: #667eea;">AND:</span> linus and kali (both terms) |
                <span style="color: #667eea;">NOT:</span> linux not kali (exclude term) |
                <span style="color: #667eea;">Quotes:</span> "exact phrase"
            </div>
        </div>

        <!-- Quick Search Buttons -->
        <div class="quick-search-section">
            <div class="quick-search-header">
                <h3>Quick Searches</h3>
                <button type="button" class="btn btn-secondary btn-small" onclick="editQuickSearches()">Edit</button>
            </div>
            <div class="quick-search-grid" id="quickSearchGrid">
                <!-- Quick search buttons will be populated here -->
            </div>
        </div>

        <!-- Add URL Form -->
        <div class="add-form">
            <form method="POST" action="{{ url_for('add') }}">
                <div class="form-row">
                    <div class="form-group">
                        <input type="text" name="title" class="form-input" placeholder="Title" required>
                    </div>
                    <div class="form-group">
                        <input type="url" name="url" class="form-input" placeholder="URL" required>
                    </div>
                    <div class="form-group">
                        <input type="text" name="tags" class="form-input" placeholder="Tags (comma-separated)">
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-secondary" onclick="document.querySelector('.add-form').style.display='none'">Cancel</button>
                </div>
            </form>
        </div>

        <!-- Summary -->
        <div class="summary">
            Showing {{ items|length }} item{{ 's' if items|length != 1 else '' }}{% if search %} (filtered){% endif %}
        </div>

        <!-- Table -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Title <span class="edit-hint">(double-click to edit)</span></th>
                        <th>Tags <span class="edit-hint">(double-click to edit)</span></th>
                        <th>Access Count</th>
                        <th>Delete</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr data-url="{{ item.url }}">
                        <td class="editable-cell" data-field="title" data-url="{{ item.url }}" title="Double-click to edit">{{ item.title }}</td>
                        <td class="editable-cell" data-field="tags" data-url="{{ item.url }}" title="Double-click to edit">{{ item.tags }}</td>
                        <td><span class="access-count">{{ item.access_count or 0 }}</span></td>
                        <td>
                            <form method="POST" action="{{ url_for('delete') }}" onsubmit="return confirm('Delete this item?')" style="display: inline;">
                                <input type="hidden" name="title" value="{{ item.title }}">
                                <input type="hidden" name="url" value="{{ item.url }}">
                                <button type="submit" class="btn btn-danger">Delete</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Duplicate URLs Modal -->
    <div id="duplicatesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Duplicate URLs Found</h2>
                <span class="close" onclick="closeDuplicatesModal()">&times;</span>
            </div>
            <div id="duplicatesContent">
                <!-- Duplicate results will be populated here -->
            </div>
        </div>
    </div>

    <!-- Edit Quick Searches Modal -->
    <div id="editQuickSearchModal" class="edit-modal">
        <div class="edit-modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Edit Quick Search Buttons</h2>
                <span class="close" onclick="closeEditQuickSearchModal()">&times;</span>
            </div>
            <div class="edit-grid" id="editQuickSearchGrid">
                <!-- Edit form will be populated here -->
            </div>
            <div class="edit-actions">
                <button type="button" class="btn btn-secondary" onclick="closeEditQuickSearchModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveQuickSearches()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div id="contextMenu" class="context-menu">
        <div class="context-menu-item" onclick="showUrlModal()">
            <span class="context-menu-icon">🔗</span>
            <span class="context-menu-text">View URL</span>
        </div>
        <div class="context-menu-item" onclick="editUrlModal()">
            <span class="context-menu-icon">✏️</span>
            <span class="context-menu-text">Edit URL</span>
        </div>
    </div>

    <!-- URL Display/Edit Modal -->
    <div id="urlModal" class="url-modal">
        <div class="url-modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="urlModalTitle">View URL</h2>
                <span class="close" onclick="closeUrlModal()">&times;</span>
            </div>
            <div id="urlModalContent">
                <!-- URL content will be populated here -->
            </div>
            <div class="modal-actions" id="urlModalActions">
                <!-- Action buttons will be populated here -->
            </div>
        </div>
    </div>

    <script>
        let lastSelected;
        let currentlyEditing = null;
        let doubleClickTimer = null;
        let contextMenuTarget = null;

        // Context menu functionality
        function showContextMenu(x, y) {
            console.log('showContextMenu called at:', x, y); // Debug log
            const contextMenu = document.getElementById('contextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'block';
                contextMenu.style.left = x + 'px';
                contextMenu.style.top = y + 'px';
            } else {
                console.error('Context menu element not found!');
            }
        }

        function hideContextMenu() {
            document.getElementById('contextMenu').style.display = 'none';
            contextMenuTarget = null;
        }

        // Hide context menu when clicking elsewhere
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.context-menu')) {
                hideContextMenu();
            }
        });

        function initializeRowEvents() {
            document.querySelectorAll('tbody tr').forEach(row => {
                row.addEventListener('click', function (e) {
                    // Don't open URL if currently editing
                    if (currentlyEditing) {
                        return;
                    }

                    // Don't open URL if clicking on buttons or form elements
                    if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' ||
                        e.target.closest('form') || e.target.closest('button')) {
                        return;
                    }

                    // For editable cells, delay the URL opening to check for double-click
                    if (e.target.classList.contains('editable-cell')) {
                        if (doubleClickTimer) {
                            // This is the second click of a double-click, cancel URL opening
                            clearTimeout(doubleClickTimer);
                            doubleClickTimer = null;
                            return;
                        }

                        // Set a timer to open URL after a short delay
                        doubleClickTimer = setTimeout(() => {
                            doubleClickTimer = null;
                            const url = row.getAttribute('data-url');
                            if (url) {
                                trackUrlAccess(url);
                                window.open(url, '_blank');
                            }
                        }, 250); // 250ms delay
                        return;
                    }

                    // For non-editable areas, open URL immediately
                    const url = row.getAttribute('data-url');
                    if (url) {
                        trackUrlAccess(url);
                        window.open(url, '_blank');
                    }
                });

                row.addEventListener('dblclick', function (e) {
                    // Clear any pending URL opening
                    if (doubleClickTimer) {
                        clearTimeout(doubleClickTimer);
                        doubleClickTimer = null;
                    }

                    // Handle double-click on editable cells
                    if (e.target.classList.contains('editable-cell')) {
                        if (currentlyEditing && currentlyEditing !== e.target) {
                            cancelEdit(currentlyEditing);
                        }
                        startEdit(e.target);
                        return;
                    }

                    // Handle double-click on non-editable areas (row selection)
                    if (!currentlyEditing) {
                        if (lastSelected) lastSelected.classList.remove('selected');
                        row.classList.add('selected');
                        lastSelected = row;
                    }
                });
            });

            // Add right-click context menu for title cells using event delegation
            const titleCells = document.querySelectorAll('.editable-cell[data-field="title"]');
            console.log('Found title cells:', titleCells.length); // Debug log
        }

        // Use event delegation for context menu on the document
        document.addEventListener('contextmenu', function(e) {
            // Check if the right-clicked element is a title cell
            if (e.target.classList.contains('editable-cell') && e.target.dataset.field === 'title') {
                console.log('Context menu triggered on title cell!'); // Debug log
                e.preventDefault();
                e.stopPropagation();

                contextMenuTarget = e.target;
                showContextMenu(e.pageX, e.pageY);
            }
        });
        }

        // Inline editing functions
        function startEdit(cell) {
            if (currentlyEditing) return;

            currentlyEditing = cell;
            const originalValue = cell.textContent.trim();

            // Create input element
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'editable-input';
            input.value = originalValue;

            // Replace cell content with input
            cell.innerHTML = '';
            cell.appendChild(input);
            cell.classList.add('editing');

            // Focus and select text
            input.focus();
            input.select();

            // Handle save on Enter
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    saveEdit(cell, input.value, originalValue);
                } else if (e.key === 'Escape') {
                    cancelEdit(cell, originalValue);
                }
            });

            // Handle save on blur (clicking outside)
            input.addEventListener('blur', function() {
                saveEdit(cell, input.value, originalValue);
            });
        }

        function cancelEdit(cell, originalValue = null) {
            if (originalValue === null) {
                originalValue = cell.querySelector('.editable-input')?.value || '';
            }

            cell.innerHTML = originalValue;
            cell.classList.remove('editing');
            currentlyEditing = null;
        }

        async function saveEdit(cell, newValue, originalValue) {
            const url = cell.getAttribute('data-url');
            const field = cell.getAttribute('data-field');

            // If value hasn't changed, just cancel edit
            if (newValue === originalValue) {
                cancelEdit(cell, originalValue);
                return;
            }

            try {
                const response = await fetch('/api/update-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        field: field,
                        value: newValue
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update cell with new value
                    cell.innerHTML = newValue;
                    cell.classList.remove('editing');
                    currentlyEditing = null;

                    // Show brief success indication
                    cell.style.background = 'rgba(102, 126, 234, 0.2)';
                    setTimeout(() => {
                        cell.style.background = '';
                    }, 1000);
                } else {
                    alert('Error updating: ' + result.error);
                    cancelEdit(cell, originalValue);
                }
            } catch (error) {
                console.error('Error saving edit:', error);
                alert('Error saving changes');
                cancelEdit(cell, originalValue);
            }
        }

        // Track URL access
        async function trackUrlAccess(url) {
            try {
                await fetch('/api/access-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url })
                });

                // Update the access count in the UI
                const row = document.querySelector(`tr[data-url="${url}"]`);
                if (row) {
                    const accessCountSpan = row.querySelector('.access-count');
                    if (accessCountSpan) {
                        const currentCount = parseInt(accessCountSpan.textContent) || 0;
                        accessCountSpan.textContent = currentCount + 1;
                    }
                }
            } catch (error) {
                console.error('Error tracking URL access:', error);
            }
        }

        // Dropdown functionality
        function toggleDropdown() {
            const dropdown = document.getElementById('toolsDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        window.addEventListener('click', function(e) {
            if (!e.target.matches('.dropdown-toggle')) {
                const dropdown = document.getElementById('toolsDropdown');
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });

        // Check for duplicate URLs
        async function checkDuplicates() {
            // Close dropdown
            document.getElementById('toolsDropdown').classList.remove('show');

            try {
                const response = await fetch('/api/check-duplicates');
                const data = await response.json();

                if (data.success) {
                    displayDuplicates(data);
                } else {
                    alert('Error checking duplicates: ' + data.error);
                }
            } catch (error) {
                console.error('Error checking duplicates:', error);
                alert('Error checking duplicates: ' + error.message);
            }
        }

        // Display duplicate results in modal
        function displayDuplicates(data) {
            const modal = document.getElementById('duplicatesModal');
            const content = document.getElementById('duplicatesContent');

            if (data.duplicates.length === 0) {
                content.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <h3>🎉 No Duplicate URLs Found!</h3>
                        <p>Your bookmark collection is clean and organized.</p>
                    </div>
                `;
            } else {
                let html = `
                    <div style="margin-bottom: 20px; padding: 15px; background: #e3f2fd; border-radius: 10px;">
                        <strong>Summary:</strong> Found ${data.total_duplicates} duplicate URL groups with ${data.total_duplicate_items} total items.
                    </div>
                `;

                data.duplicates.forEach(duplicate => {
                    html += `
                        <div class="duplicate-group">
                            <div class="duplicate-url">
                                🔗 ${duplicate.url} (${duplicate.count} copies)
                            </div>
                            <div class="duplicate-items">
                    `;

                    duplicate.items.forEach((item, index) => {
                        html += `
                            <div class="duplicate-item">
                                <div class="duplicate-item-title">${index + 1}. ${item.title || 'Untitled'}</div>
                                ${item.tags ? `<div class="duplicate-item-tags">Tags: ${item.tags}</div>` : ''}
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                content.innerHTML = html;
            }

            modal.style.display = 'block';
        }

        // Close duplicates modal
        function closeDuplicatesModal() {
            document.getElementById('duplicatesModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('duplicatesModal');
            if (e.target === modal) {
                closeDuplicatesModal();
            }
        });

        // Quick Search functionality
        let quickSearches = [];

        // Load quick searches on page load
        async function loadQuickSearches() {
            try {
                const response = await fetch('/api/quick-searches');
                const data = await response.json();

                if (data.success) {
                    quickSearches = data.quick_searches;
                    renderQuickSearchButtons();
                }
            } catch (error) {
                console.error('Error loading quick searches:', error);
                // Initialize with empty buttons
                quickSearches = Array(8).fill().map(() => ({label: '', search: ''}));
                renderQuickSearchButtons();
            }
        }

        // Render quick search buttons
        function renderQuickSearchButtons() {
            const grid = document.getElementById('quickSearchGrid');
            grid.innerHTML = '';

            quickSearches.forEach((item, index) => {
                const button = document.createElement('div');
                button.className = `quick-search-btn ${item.search ? 'active' : 'empty'}`;
                button.onclick = () => performQuickSearch(item.search);

                if (item.search) {
                    button.innerHTML = `
                        <div class="quick-search-icon">🔍</div>
                        <div class="quick-search-label">${item.label || item.search}</div>
                    `;
                } else {
                    button.innerHTML = `
                        <div class="quick-search-icon">➕</div>
                        <div class="quick-search-label">Empty Slot</div>
                    `;
                }

                grid.appendChild(button);
            });
        }

        // Perform quick search
        function performQuickSearch(searchTerm) {
            if (searchTerm) {
                const searchInput = document.querySelector('input[name="search"]');
                searchInput.value = searchTerm;
                searchInput.form.submit();
            }
        }

        // Edit quick searches
        function editQuickSearches() {
            const modal = document.getElementById('editQuickSearchModal');
            const grid = document.getElementById('editQuickSearchGrid');

            grid.innerHTML = '';

            quickSearches.forEach((item, index) => {
                const editItem = document.createElement('div');
                editItem.className = 'edit-item';
                editItem.innerHTML = `
                    <h4>Button ${index + 1}</h4>
                    <input type="text" class="edit-input" placeholder="Display Label"
                           value="${item.label}" data-index="${index}" data-field="label">
                    <input type="text" class="edit-input" placeholder="Search Term"
                           value="${item.search}" data-index="${index}" data-field="search">
                `;
                grid.appendChild(editItem);
            });

            modal.style.display = 'block';
        }

        // Save quick searches
        async function saveQuickSearches() {
            const inputs = document.querySelectorAll('#editQuickSearchGrid .edit-input');
            const newQuickSearches = Array(8).fill().map(() => ({label: '', search: ''}));

            inputs.forEach(input => {
                const index = parseInt(input.dataset.index);
                const field = input.dataset.field;
                newQuickSearches[index][field] = input.value.trim();
            });

            try {
                const response = await fetch('/api/quick-searches', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        quick_searches: newQuickSearches
                    })
                });

                const data = await response.json();

                if (data.success) {
                    quickSearches = newQuickSearches;
                    renderQuickSearchButtons();
                    closeEditQuickSearchModal();
                } else {
                    alert('Error saving quick searches: ' + data.error);
                }
            } catch (error) {
                console.error('Error saving quick searches:', error);
                alert('Error saving quick searches: ' + error.message);
            }
        }

        // Close edit modal
        function closeEditQuickSearchModal() {
            document.getElementById('editQuickSearchModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const editModal = document.getElementById('editQuickSearchModal');
            if (e.target === editModal) {
                closeEditQuickSearchModal();
            }
        });

        // URL Modal functionality
        function showUrlModal() {
            hideContextMenu();

            if (!contextMenuTarget) return;

            const url = contextMenuTarget.closest('tr').getAttribute('data-url');
            const title = contextMenuTarget.textContent.trim();

            const modal = document.getElementById('urlModal');
            const modalTitle = document.getElementById('urlModalTitle');
            const modalContent = document.getElementById('urlModalContent');
            const modalActions = document.getElementById('urlModalActions');

            modalTitle.textContent = 'View URL';
            modalContent.innerHTML = `
                <h4>Title: ${title}</h4>
                <div class="url-display">${url}</div>
            `;
            modalActions.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeUrlModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="copyUrlToClipboard('${url}')">Copy URL</button>
            `;

            modal.style.display = 'block';
        }

        function editUrlModal() {
            hideContextMenu();

            if (!contextMenuTarget) return;

            const url = contextMenuTarget.closest('tr').getAttribute('data-url');
            const title = contextMenuTarget.textContent.trim();

            const modal = document.getElementById('urlModal');
            const modalTitle = document.getElementById('urlModalTitle');
            const modalContent = document.getElementById('urlModalContent');
            const modalActions = document.getElementById('urlModalActions');

            modalTitle.textContent = 'Edit URL';
            modalContent.innerHTML = `
                <h4>Title: ${title}</h4>
                <label for="urlEditInput">URL:</label>
                <input type="url" id="urlEditInput" class="url-edit-input" value="${url}" placeholder="Enter URL">
            `;
            modalActions.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeUrlModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveUrlEdit()">Save Changes</button>
            `;

            modal.style.display = 'block';

            // Focus and select the URL input
            setTimeout(() => {
                const input = document.getElementById('urlEditInput');
                input.focus();
                input.select();
            }, 100);
        }

        async function saveUrlEdit() {
            const newUrl = document.getElementById('urlEditInput').value.trim();
            const oldUrl = contextMenuTarget.closest('tr').getAttribute('data-url');

            if (!newUrl) {
                alert('URL cannot be empty');
                return;
            }

            if (newUrl === oldUrl) {
                closeUrlModal();
                return;
            }

            try {
                const response = await fetch('/api/update-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: oldUrl,
                        field: 'url',
                        value: newUrl
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update the row's data-url attribute
                    contextMenuTarget.closest('tr').setAttribute('data-url', newUrl);
                    closeUrlModal();

                    // Show brief success indication
                    const row = contextMenuTarget.closest('tr');
                    row.style.background = 'rgba(102, 126, 234, 0.2)';
                    setTimeout(() => {
                        row.style.background = '';
                    }, 1000);
                } else {
                    alert('Error updating URL: ' + result.error);
                }
            } catch (error) {
                console.error('Error saving URL:', error);
                alert('Error saving URL: ' + error.message);
            }
        }

        function copyUrlToClipboard(url) {
            navigator.clipboard.writeText(url).then(() => {
                // Show brief success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#28a745';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '';
                }, 1500);
            }).catch(err => {
                console.error('Failed to copy URL:', err);
                alert('Failed to copy URL to clipboard');
            });
        }

        function closeUrlModal() {
            document.getElementById('urlModal').style.display = 'none';
            contextMenuTarget = null;
        }

        // Close URL modal when clicking outside
        window.addEventListener('click', function(e) {
            const urlModal = document.getElementById('urlModal');
            if (e.target === urlModal) {
                closeUrlModal();
            }
        });

        // Initialize on page load
        initializeRowEvents();
        loadQuickSearches();
    </script>
</body>
</html>
