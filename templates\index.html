<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Pocket Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        #title-bar {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        #title-bar h1 {
            margin: 0;
            font-size: 24px;
        }

        button {
            margin-left: 5px;
            padding: 6px 12px;
            font-size: 14px;
            cursor: pointer;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1em;
        }

        th, td {
            padding: 10px;
            border-bottom: 1px solid #ccc;
        }

        tr:hover {
            background-color: #f0f0f0;
            cursor: pointer;
        }

        .selected {
            background-color: #d0ebff !important;
        }

        input[type="text"], input[type="url"] {
            padding: 6px;
            margin-right: 5px;
            font-size: 14px;
        }

        .delete-btn {
            background-color: red;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
        }

        #add-form {
            display: none;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            padding: 15px;
            background: #f9f9f9;
        }

        .url-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 5px;
        }

        .url-status.accessible {
            background-color: #28a745;
        }

        .url-status.error {
            background-color: #dc3545;
        }

        .url-status.checking {
            background-color: #ffc107;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        #check-urls-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .access-count {
            text-align: center;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>

    <!-- ✅ Title Bar -->
    <div id="title-bar">
        <h1>Pocket Clone by Kevin Bywater</h1>
    </div>

    <!-- ✅ Control Bar -->
    <div style="margin: 15px 0;">
        <form method="GET" action="{{ url_for('index') }}" style="display: inline;">
            <input type="text" id="search" name="search" placeholder="Search titles or tags..." value="{{ search }}">
            <button type="submit">Search</button>
        </form>

        <a href="{{ url_for('index') }}">
            <button type="button">Clear Filters</button>
        </a>

        <button type="button" onclick="document.getElementById('add-form').style.display='block'">Add URL</button>
    </div>

    <!-- ✅ Add URL Form -->
    <div id="add-form">
        <form method="POST" action="{{ url_for('add') }}">
            <input type="text" name="title" placeholder="Title" required>
            <input type="url" name="url" placeholder="URL" required>
            <input type="text" name="tags" placeholder="Tags (comma-separated)">
            <button type="submit">Save</button>
            <button type="button" onclick="document.getElementById('add-form').style.display='none'">Cancel</button>
        </form>
    </div>

    <!-- ✅ Table -->
    <table id="item-table">
        <thead>
            <tr>
                <th>Title</th>
                <th>Tags</th>
                <th>Access Count</th>
                <th>Status</th>
                <th>Delete</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr data-url="{{ item.url }}">
                <td>{{ item.title }}</td>
                <td>{{ item.tags }}</td>
                <td class="access-count">{{ item.access_count or 0 }}</td>
                <td>
                    <span class="url-status" data-url="{{ item.url }}" title="Click 'Check All URLs' to verify"></span>
                </td>
                <td>
                    <form method="POST" action="{{ url_for('delete') }}" onsubmit="return confirm('Delete this item?')">
                        <input type="hidden" name="title" value="{{ item.title }}">
                        <input type="hidden" name="url" value="{{ item.url }}">
                        <button type="submit" class="delete-btn">Delete</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- ✅ Pagination -->
    <div style="margin-top: 15px;">
        {% if page > 1 %}
            <a href="{{ url_for('index', page=page-1, search=search, tag=selected_tag) }}">Previous</a>
        {% endif %}
        Page {{ page }} of {{ total_pages }}
        {% if page < total_pages %}
            <a href="{{ url_for('index', page=page+1, search=search, tag=selected_tag) }}">Next</a>
        {% endif %}
    </div>

    <!-- ✅ Row Click Logic -->
    <script>
        let lastSelected;
        document.querySelectorAll('#item-table tbody tr').forEach(row => {
            row.addEventListener('click', function () {
                if (lastSelected) lastSelected.classList.remove('selected');
                row.classList.add('selected');
                lastSelected = row;
            });

            row.addEventListener('dblclick', function () {
                const url = row.getAttribute('data-url');
                if (url) {
                    // Track URL access
                    trackUrlAccess(url);
                    window.open(url, '_blank');
                }
            });
        });

        // Track URL access
        async function trackUrlAccess(url) {
            try {
                await fetch('/api/access-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url })
                });

                // Update the access count in the UI
                const row = document.querySelector(`tr[data-url="${url}"]`);
                if (row) {
                    const accessCountCell = row.querySelector('.access-count');
                    if (accessCountCell) {
                        const currentCount = parseInt(accessCountCell.textContent) || 0;
                        accessCountCell.textContent = currentCount + 1;
                    }
                }
            } catch (error) {
                console.error('Error tracking URL access:', error);
            }
        }

        // URL checking functionality
        async function checkAllUrls() {
            const button = document.getElementById('check-urls-btn');
            const statusIndicators = document.querySelectorAll('.url-status');

            // Disable button and show checking state
            button.disabled = true;
            button.textContent = 'Checking...';

            // Set all indicators to checking state
            statusIndicators.forEach(indicator => {
                indicator.className = 'url-status checking';
                indicator.title = 'Checking URL...';
            });

            try {
                const response = await fetch('/api/check-urls');
                const data = await response.json();

                if (data.success) {
                    // Update status indicators based on results
                    data.results.forEach(result => {
                        const indicator = document.querySelector(`[data-url="${result.url}"]`);
                        if (indicator) {
                            indicator.className = `url-status ${result.status}`;
                            if (result.status === 'accessible') {
                                indicator.title = `URL is accessible (Status: ${result.status_code})`;
                            } else {
                                indicator.title = `URL error: ${result.error || 'Status code: ' + result.status_code}`;
                            }
                        }
                    });
                } else {
                    alert('Error checking URLs: ' + data.error);
                    // Reset indicators on error
                    statusIndicators.forEach(indicator => {
                        indicator.className = 'url-status';
                        indicator.title = 'Click "Check All URLs" to verify';
                    });
                }
            } catch (error) {
                alert('Error checking URLs: ' + error.message);
                // Reset indicators on error
                statusIndicators.forEach(indicator => {
                    indicator.className = 'url-status';
                    indicator.title = 'Click "Check All URLs" to verify';
                });
            } finally {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Check All URLs';
            }
        }
    </script>
</body>
</html>